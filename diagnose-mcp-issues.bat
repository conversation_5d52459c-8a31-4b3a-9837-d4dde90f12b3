@echo off
echo MCP服务器诊断工具
echo ==================
echo.

echo 1. 系统环境检查
echo ------------------
echo Python版本:
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python未安装
) else (
    echo ✅ Python已安装
)

echo.
echo Node.js版本:
node --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装
) else (
    echo ✅ Node.js已安装
)

echo.
echo NPM版本:
npm --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ NPM未安装
) else (
    echo ✅ NPM已安装
)

echo.
echo 2. Python依赖检查
echo ------------------
python -c "import fastmcp; print('✅ fastmcp已安装')" 2>nul || echo ❌ fastmcp未安装
python -c "import requests; print('✅ requests已安装')" 2>nul || echo ❌ requests未安装

echo.
echo 3. NPM包检查
echo ------------------
npm list @sylphlab/pdf-reader-mcp >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ @sylphlab/pdf-reader-mcp已安装
) else (
    echo ❌ @sylphlab/pdf-reader-mcp未安装
)

echo.
echo 4. 文件存在性检查
echo ------------------
if exist "pdf-mcp-server\server.py" (
    echo ✅ PDF服务器文件存在
) else (
    echo ❌ PDF服务器文件不存在
)

if exist "pdf-mcp-server\.venv" (
    echo ✅ Python虚拟环境存在
) else (
    echo ❌ Python虚拟环境不存在
)

if exist "claudia-mcp-config.json" (
    echo ✅ MCP配置文件存在
) else (
    echo ❌ MCP配置文件不存在
)

echo.
echo 5. PDF工具检查
echo ------------------
pdftk --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PDFtk已安装
) else (
    echo ❌ PDFtk未安装
)

qpdf --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ QPDF已安装
) else (
    echo ❌ QPDF未安装
)

echo.
echo 6. 端口检查
echo ------------------
netstat -an | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口3000被占用
) else (
    echo ✅ 端口3000可用
)

echo.
echo 诊断完成！
echo.
echo 修复建议：
echo 1. 如果Python未安装，请安装Python 3.8+
echo 2. 如果依赖未安装，运行 fix-mcp-servers.bat
echo 3. 如果PDF工具未安装，请安装PDFtk和QPDF
echo 4. 重启Claudia并重新导入MCP配置
pause
