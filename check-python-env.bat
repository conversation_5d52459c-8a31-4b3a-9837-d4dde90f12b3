@echo off
echo 检查Python环境...
echo.

echo 1. 检查Python版本:
python --version
if %errorlevel% neq 0 (
    echo Python未安装或未添加到PATH
    echo 请从 https://www.python.org/downloads/ 下载并安装Python
    pause
    exit /b 1
)

echo.
echo 2. 检查pip版本:
pip --version
if %errorlevel% neq 0 (
    echo pip未安装
    pause
    exit /b 1
)

echo.
echo 3. 检查已安装的Python包:
pip list | findstr fastmcp
pip list | findstr requests

echo.
echo 4. 检查PDF工具:
echo 检查PDFtk...
pdftk --version 2>nul
if %errorlevel% neq 0 (
    echo PDFtk未安装
)

echo 检查QPDF...
qpdf --version 2>nul
if %errorlevel% neq 0 (
    echo QPDF未安装
)

echo.
echo Python环境检查完成
pause
