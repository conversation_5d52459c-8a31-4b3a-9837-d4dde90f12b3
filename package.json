{"name": "idlegame", "uuid": "d7d09da7-3476-4f62-91cb-61d527ea4910", "creator": {"version": "3.8.6"}, "scripts": {"dev": "cocos-creator --project . --mode=dev", "build": "cocos-creator --project . --build", "build:wechat": "cocos-creator --project . --build platform=wechatgame", "build:douyin": "cocos-creator --project . --build platform=bytedance-mini-game", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint assets/scripts --ext .ts", "lint:fix": "eslint assets/scripts --ext .ts --fix", "format": "prettier --write \"assets/scripts/**/*.ts\"", "clean": "rimraf build temp library", "test:ai": "cd scripts/ai-testing && node ai-test-working.js", "test:ai:simple": "cd scripts/ai-testing && node simple-test.js", "fix:quality": "cd scripts/ai-testing && node code-quality-fixer.js", "server:test": "cd scripts/ai-testing && node test-server-simple.js", "dev:full": "npm run server:test & npm run test:ai"}, "devDependencies": {"@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "eslint": "^8.37.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.7", "rimraf": "^4.4.1", "ts-jest": "^29.1.0", "typescript": "^5.0.2"}, "version": "1.0.0", "description": "", "main": ".eslintrc.js", "repository": {"type": "git", "url": "git+https://github.com/wlzzds/COCOS_IdelGame.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/wlzzds/COCOS_IdelGame/issues"}, "homepage": "https://github.com/wlzzds/COCOS_IdelGame#readme", "dependencies": {"@sylphlab/pdf-reader-mcp": "^0.3.23", "cors": "^2.8.5", "express": "^5.1.0", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "tesseract.js": "^6.0.1"}}