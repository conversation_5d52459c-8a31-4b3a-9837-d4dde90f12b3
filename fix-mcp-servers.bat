@echo off
echo 修复MCP服务器...
echo.

echo 1. 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    echo 请安装Python 3.8+: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 2. 安装PDF MCP服务器依赖...
cd pdf-mcp-server
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 无法安装Python依赖
    pause
    exit /b 1
)
cd ..

echo.
echo 3. 检查NPM包...
echo 检查 @sylphlab/pdf-reader-mcp...
npm list @sylphlab/pdf-reader-mcp
if %errorlevel% neq 0 (
    echo 安装 @sylphlab/pdf-reader-mcp...
    npm install @sylphlab/pdf-reader-mcp
)

echo.
echo 4. 测试PDF MCP服务器...
echo 测试Python服务器...
cd pdf-mcp-server
python -c "import fastmcp; print('FastMCP导入成功')"
if %errorlevel% neq 0 (
    echo 错误: FastMCP导入失败
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo 5. 创建虚拟环境（推荐）...
if not exist "pdf-mcp-server\.venv" (
    echo 创建虚拟环境...
    cd pdf-mcp-server
    python -m venv .venv
    call .venv\Scripts\activate
    pip install -r requirements.txt
    cd ..
    echo 虚拟环境创建完成
)

echo.
echo 6. 更新MCP配置...
echo 建议使用虚拟环境路径更新claudia-mcp-config.json

echo.
echo MCP服务器修复完成！
echo.
echo 下一步：
echo 1. 重启Claudia
echo 2. 重新导入MCP配置
echo 3. 测试连接
pause
