@echo off
chcp 65001 >nul
echo 🚀 MCP快速修复工具
echo ===================
echo.

echo 📋 问题分析：
echo - Python未安装或PATH配置错误
echo - 需要安装必要的依赖包
echo.

echo 🔧 快速解决方案：
echo.

echo 1️⃣ 请先手动安装Python：
echo    访问：https://www.python.org/downloads/
echo    ⚠️ 安装时务必勾选"Add Python to PATH"
echo.
echo 2️⃣ 安装完成后按任意键继续...
pause

echo.
echo 3️⃣ 验证Python安装...
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python仍未正确安装
    echo 请确保：
    echo - 已安装Python 3.8+
    echo - 已勾选"Add Python to PATH"
    echo - 已重启命令提示符
    echo.
    echo 如果问题持续，请：
    echo 1. 重启电脑
    echo 2. 重新运行此脚本
    pause
    exit /b 1
)

echo ✅ Python安装正确
echo.

echo 4️⃣ 安装Python依赖...
pip install fastmcp requests
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    echo 尝试升级pip：
    python -m pip install --upgrade pip
    pip install fastmcp requests
)

echo.
echo 5️⃣ 验证依赖安装...
python -c "import fastmcp; print('✅ FastMCP安装成功')" 2>nul || echo ❌ FastMCP安装失败
python -c "import requests; print('✅ Requests安装成功')" 2>nul || echo ❌ Requests安装失败

echo.
echo 6️⃣ 安装NPM依赖（可选）...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    npm install @sylphlab/pdf-reader-mcp
    echo ✅ NPM依赖安装完成
) else (
    echo ⚠️ Node.js未安装，跳过NPM依赖
)

echo.
echo 7️⃣ 创建简化的MCP配置...
echo {> simple-mcp-config.json
echo   "mcpServers": {>> simple-mcp-config.json
echo     "pdf-tools": {>> simple-mcp-config.json
echo       "command": "python",>> simple-mcp-config.json
echo       "args": ["./pdf-mcp-server/server.py"]>> simple-mcp-config.json
echo     }>> simple-mcp-config.json
echo   }>> simple-mcp-config.json
echo }>> simple-mcp-config.json

echo ✅ 创建了简化配置文件：simple-mcp-config.json

echo.
echo 🎉 修复完成！
echo.
echo 📝 下一步：
echo 1. 重启Claudia
echo 2. 导入 simple-mcp-config.json
echo 3. 测试MCP连接
echo.
echo 如果仍有问题，请查看"手动安装指南.md"
pause
