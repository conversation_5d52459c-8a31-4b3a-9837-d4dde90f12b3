# 🔧 MCP服务器手动安装指南

## 🚨 当前问题
从你的截图可以看出，系统缺少以下关键组件：
- ❌ Python未安装或未添加到PATH
- ❌ Node.js可能配置有问题
- ❌ 缺少必要的依赖包

## 📋 解决步骤

### 步骤1：安装Python
1. **下载Python**
   - 访问：https://www.python.org/downloads/
   - 下载最新的Python 3.8+版本

2. **安装Python**
   - 运行下载的安装程序
   - ⚠️ **重要**：勾选"Add Python to PATH"选项
   - 选择"Install Now"

3. **验证安装**
   ```cmd
   python --version
   pip --version
   ```

### 步骤2：安装Node.js（如果需要）
1. **下载Node.js**
   - 访问：https://nodejs.org/
   - 下载LTS版本

2. **安装Node.js**
   - 运行安装程序
   - 使用默认设置

3. **验证安装**
   ```cmd
   node --version
   npm --version
   ```

### 步骤3：安装Python依赖
```cmd
pip install fastmcp requests pathlib2
```

### 步骤4：安装NPM依赖
```cmd
npm install @sylphlab/pdf-reader-mcp
```

### 步骤5：创建虚拟环境（推荐）
```cmd
cd pdf-mcp-server
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements.txt
```

## 🔄 替代方案

### 方案1：使用Chocolatey包管理器
```cmd
# 以管理员身份运行PowerShell
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装Python和Node.js
choco install python nodejs -y
```

### 方案2：使用Winget（Windows 10/11）
```cmd
winget install Python.Python.3
winget install OpenJS.NodeJS
```

## 🧪 验证安装

运行以下命令验证所有组件：
```cmd
python --version
pip --version
node --version
npm --version
python -c "import fastmcp; print('FastMCP OK')"
```

## 🔧 修复MCP配置

安装完成后，更新MCP配置文件：

### 选项1：使用系统Python
```json
{
  "mcpServers": {
    "pdf-tools": {
      "command": "python",
      "args": ["./pdf-mcp-server/server.py"]
    }
  }
}
```

### 选项2：使用虚拟环境（推荐）
```json
{
  "mcpServers": {
    "pdf-tools": {
      "command": "./pdf-mcp-server/.venv/Scripts/python.exe",
      "args": ["./pdf-mcp-server/server.py"]
    }
  }
}
```

## 🚀 最终步骤

1. **重启命令提示符**
2. **运行诊断脚本**：`diagnose-mcp-issues.bat`
3. **重启Claudia**
4. **重新导入MCP配置**

## 🆘 如果仍有问题

1. **检查环境变量**
   - 确保Python和pip在PATH中
   - 重启电脑可能有帮助

2. **使用完整路径**
   - 找到Python安装路径（通常在`C:\Users\<USER>\AppData\Local\Programs\Python\`）
   - 在MCP配置中使用完整路径

3. **联系支持**
   - 提供`python --version`和`pip --version`的输出
   - 提供具体的错误信息
