@echo off
chcp 65001 >nul
echo 🔧 MCP服务器环境安装向导
echo ================================
echo.

echo 📋 检测到的问题：
echo ❌ Python未安装或未添加到PATH
echo ❌ Node.js可能未正确配置
echo ❌ 缺少必要的依赖包
echo.

echo 🚀 开始自动安装...
echo.

echo 1️⃣ 检查Chocolatey包管理器...
where choco >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装Chocolatey包管理器...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if %errorlevel% neq 0 (
        echo ❌ Chocolatey安装失败，请手动安装Python
        echo 请访问: https://www.python.org/downloads/
        pause
        exit /b 1
    )
    echo ✅ Chocolatey安装成功
) else (
    echo ✅ Chocolatey已安装
)

echo.
echo 2️⃣ 安装Python...
choco install python -y
if %errorlevel% neq 0 (
    echo ⚠️ Chocolatey安装失败，尝试直接下载...
    echo 请手动安装Python 3.8+: https://www.python.org/downloads/
    echo 安装时请勾选"Add Python to PATH"选项
    pause
    exit /b 1
)

echo.
echo 3️⃣ 刷新环境变量...
call refreshenv

echo.
echo 4️⃣ 验证Python安装...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python安装失败或PATH配置错误
    echo 请重启命令提示符或重启电脑后重试
    pause
    exit /b 1
)

echo.
echo 5️⃣ 安装Node.js（如果需要）...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装Node.js...
    choco install nodejs -y
    call refreshenv
)

echo.
echo 6️⃣ 安装Python依赖...
pip install fastmcp requests pathlib2
if %errorlevel% neq 0 (
    echo ❌ Python依赖安装失败
    pause
    exit /b 1
)

echo.
echo 7️⃣ 安装NPM依赖...
npm install @sylphlab/pdf-reader-mcp
if %errorlevel% neq 0 (
    echo ⚠️ NPM包安装失败，但可以继续
)

echo.
echo ✅ 安装完成！
echo.
echo 📝 下一步：
echo 1. 重启命令提示符
echo 2. 运行 diagnose-mcp-issues.bat 验证安装
echo 3. 重启Claudia并重新导入MCP配置
echo.
pause
